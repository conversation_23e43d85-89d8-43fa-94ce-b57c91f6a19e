<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-step {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .test-step h3 {
            margin-top: 0;
            color: #333;
        }
        .test-step p {
            margin: 5px 0;
        }
        .expected {
            color: #007700;
            font-weight: bold;
        }
        .issue {
            color: #cc0000;
            font-weight: bold;
        }
        .fixed {
            color: #0066cc;
            font-weight: bold;
        }
        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Modal Interaction Fix Test</h1>
        <p>This test verifies the fix for the modal interaction issue where the times toggle button stops working after opening/closing the learning modal when the stats modal is open.</p>
        
        <div class="test-step">
            <h3>Test Scenario</h3>
            <p><strong>Steps to reproduce the issue:</strong></p>
            <ol>
                <li>Open and close the learning modal</li>
                <li>Open the stats modal (more stats)</li>
                <li>Try to close the stats modal using the times toggle button</li>
            </ol>
            <p class="issue">Previous Issue: Times toggle button would not work to close the stats modal</p>
            <p class="fixed">Fixed: Times toggle button now works correctly in all scenarios</p>
        </div>

        <div class="test-step">
            <h3>Root Cause Analysis</h3>
            <p>The issue was in the learning modal's interaction with the times toggle button:</p>
            <ul>
                <li>Learning modal was trying to restore times toggle state even when it never took control</li>
                <li>When stats modal was open, learning modal should not interfere with times toggle</li>
                <li>Restoration logic was breaking the times toggle functionality</li>
            </ul>
        </div>

        <div class="test-step">
            <h3>Fix Implementation</h3>
            <p>Added proper state tracking and conditional restoration:</p>
            <div class="code">
// Track whether learning modal took control of times toggle
let learningModalControlsTimesToggle = false;

// In openLearningModal():
if (isStatsModalOpen) {
    learningModalControlsTimesToggle = false; // Don't take control
} else {
    learningModalControlsTimesToggle = true;  // Take control
    // ... modify times toggle
}

// In closeLearningModal():
if (learningModalControlsTimesToggle) {
    // Only restore if we actually took control
    // ... restore times toggle
} else {
    // Just handle times panel state, don't touch toggle
}
            </div>
        </div>

        <div class="test-step">
            <h3>Test Cases</h3>
            <p><strong>Case 1: Normal learning modal usage</strong></p>
            <p class="expected">✓ Learning modal takes control of times toggle, restores properly on close</p>
            
            <p><strong>Case 2: Learning modal with stats modal open</strong></p>
            <p class="expected">✓ Learning modal doesn't interfere with times toggle, stats modal retains control</p>
            
            <p><strong>Case 3: Times panel state preservation</strong></p>
            <p class="expected">✓ Times panel state is preserved and restored correctly in both scenarios</p>
        </div>

        <div class="test-step">
            <h3>Verification Steps</h3>
            <ol>
                <li>Open the main application</li>
                <li>Test Case 1: Open learning modal → close with times toggle → verify times toggle works normally</li>
                <li>Test Case 2: Open learning modal → close → open stats modal → verify times toggle closes stats modal</li>
                <li>Test Case 3: Open times panel → open learning modal → close → verify times panel state</li>
                <li>Test Case 4: Open times panel → open learning modal → open stats modal → close learning modal → verify times panel and stats modal work</li>
            </ol>
        </div>

        <div class="test-step">
            <h3>Expected Results</h3>
            <p class="expected">All test cases should pass:</p>
            <ul>
                <li>Times toggle button always works to close the stats modal</li>
                <li>Learning modal doesn't break times toggle functionality</li>
                <li>Times panel state is preserved correctly</li>
                <li>No interference between modals</li>
            </ul>
        </div>
    </div>
</body>
</html>
