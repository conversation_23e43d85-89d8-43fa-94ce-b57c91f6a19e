// Russian language file
export default {
  dir: "ltr",
  settings: {
    title: "Настройки",
    save: "Сохранить",
    close: "Закрыть",
    language: "Язык",
  },
  info: {
    title: "Информация",
    pwaInstall: {
      title: "Установить как приложение",
      description:
        "Установите scTimer как прогрессивное веб-приложение для лучшего опыта. Работает офлайн и ощущается как нативное приложение.",
      install: "Установить приложение",
      iosTitle: "Установка iOS/iPad:",
      iosStep1: "1. Нажмите кнопку Поделиться",
      iosStep2: '2. Прокрутите вниз и нажмите "На экран Домой"',
      iosStep3: '3. Нажмите "Добавить" для установки',
      note: "Доступно в Chrome, Safari и других современных браузерах",
    },
    shortcuts: {
      title: "Горячие клавиши",
      timer: "Управление таймером",
      spacebar: "Запуск/остановка таймера",
      escape: "Отменить инспекцию и закрыть модальные окна",
      navigation: "Навигация и действия",
      generate: "Генерировать новую скрамбл",
      list: "Переключить список времён",
      settings: "Открыть настройки",
      edit: "Редактировать текущую скрамбл",
      copy: "Копировать скрамбл в буфер обмена",
      stats: "Открыть подробную статистику",
      display: "Переключить отображение",
      visualization: "Переключить визуализацию головоломки",
      statistics: "Переключить отображение статистики",
      darkMode: "Переключить тёмный режим",
      inspection: "Переключить WCA инспекцию",
      penalties: "Управление штрафами",
      removePenalty: "Убрать штраф с последней сборки",
      addPlus2: "Добавить штраф +2 к последней сборке",
      addDNF: "Добавить штраф DNF к последней сборке",
      session: "Управление сессиями",
      emptySession: "Очистить текущую сессию",
      exportSession: "Экспортировать текущую сессию",
      eventSwitching: "Переключение дисциплин",
      alt2to7: "Переключиться на кубики 2×2×2 до 7×7×7",
      altP: "Переключиться на Пираминкс",
      altM: "Переключиться на Мегаминкс",
      altC: "Переключиться на Клок",
      altS: "Переключиться на Скьюб",
      alt1: "Переключиться на Скваер-1",
      altF: "Переключиться на 3×3×3 Наименьшее количество ходов",
      altO: "Переключиться на 3×3×3 Одной рукой",
      blindfolded: "Дисциплины вслепую",
      altCtrl3: "Переключиться на 3×3×3 Вслепую",
      altCtrl4: "Переключиться на 4×4×4 Вслепую",
      altCtrl5: "Переключиться на 5×5×5 Вслепую",
      altCtrl6: "Переключиться на 3×3×3 Мульти-вслепую",
      sessionMgmt: "Управление сессиями",
      altN: "Создать новую сессию",
    },
    gestures: {
      title: "Мобильные жесты",
      swipeDown: "Свайп вниз",
      swipeDownDesc: "Удалить последнюю сборку",
      swipeUp: "Свайп вверх",
      swipeUpDesc: "Переключить штрафы (нет/+2/DNF)",
      swipeLeft: "Свайп влево",
      swipeLeftDesc: "LTR: Новая скрамбл | RTL: Список времён",
      swipeRight: "Свайп вправо",
      swipeRightDesc: "LTR: Список времён | RTL: Новая скрамбл",
      doubleClick: "Двойной клик",
      doubleClickDesc: "Копировать текущую скрамбл (ПК/Мобильный)",
      longPress: "Долгое нажатие/Клик и удержание",
      longPressDesc: "Редактировать текущую скрамбл (ПК/Мобильный)",
    },
    features: {
      title: "Основные функции",
      timer: "Профессиональный таймер",
      timerDesc: "WCA-совместимый хронометраж с режимом инспекции",
      puzzles: "Все WCA дисциплины",
      puzzlesDesc: "Полная поддержка всех официальных WCA дисциплин",
      statistics: "Продвинутая статистика",
      statisticsDesc: "Подробная аналитика с ao5, ao12, ao100",
      scrambles: "Официальные скрамблы",
      scramblesDesc: "Генерация скрамблов по стандарту WCA с 2D визуализацией",
      multilingual: "Многоязычная поддержка",
      multilingualDesc: "15+ языков с поддержкой RTL",
      sync: "Синхронизация Google Drive",
      syncDesc: "Кроссплатформенная синхронизация с умным слиянием",
    },
    sync: {
      title: "Синхронизация Google Drive",
      description:
        "Синхронизируйте ваши времена сборки на всех устройствах используя Google Drive. Ваши данные безопасно хранятся в вашем личном аккаунте Google Drive.",
      secure: "Безопасно и приватно",
      automatic: "Автоматическая синхронизация",
      offline: "Поддержка офлайн",
      smartMerge: "Умное слияние",
      note: "Включите синхронизацию Google Drive в настройках, чтобы держать ваши времена синхронизированными на всех устройствах.",
    },
  },
  timerOptions: {
    title: "Настройки таймера",
    warningSounds: "Включить предупреждающие звуки",
    useInspection: "Использовать WCA инспекцию (15с)",
    inspectionSound: "Звук инспекции:",
    inspectionSoundNone: "Нет",
    inspectionSoundVoice: "Голос",
    inspectionSoundBeep: "Сигнал",
    stackmatResetInspection: "Сброс Stackmat запускает инспекцию",
    stackmatResetNote: "Примечание: Работает только когда таймер не на 0.000",
    inputTimer: "Режим ввода таймера (Вводить времена вручную)",
    timerMode: "Режим таймера:",
    timerModeTimer: "Таймер",
    timerModeTyping: "Ввод",
    timerModeStackmat: "Stackmat",
    timerModeBluetooth: "Bluetooth (Скоро)",
    microphoneInput: "Ввод микрофона",
    microphoneAuto: "Автоопределение",
    microphoneNote: "Выберите ваш Y-разветвитель или внешний микрофон",
    decimalPlaces: "Десятичные знаки:",
    decimalPlacesNone: "Нет (12)",
    decimalPlaces1: "1 (12.3)",
    decimalPlaces2: "2 (12.34)",
    decimalPlaces3: "3 (12.345)",
  },
  displayOptions: {
    title: "Настройки отображения",
    showVisualization: "Показать визуализацию головоломки",
    showStats: "Показать статистику",
    showDebug: "Показать отладочную информацию",
    darkMode: "Тёмный режим",
    showFMCKeyboard: "Показать FMC клавиатуру",
    scrambleFontSize: "Размер шрифта скрамбла",
  },
  app: {
    title: "scTimer",
    description: "Таймер для спидкубинга с WCA инспекцией и статистикой",
    enterTime: "Ввести время",
    enterSolveTime: "Ввести время сборки вручную",
    generateScrambles: "Генерировать скрамблы",
    outOf: "Из:",
    numberOfCubes: "Количество кубиков (минимум 2):",
    numberOfCubesSolved: "Количество собранных кубиков:",
  },
  timer: {
    ready: "Готов",
    running: "Работает",
    idle: "Ожидание",
    inspection: "Инспекция",
    holding: "Удержание",
  },
  stats: {
    title: "Статистика",
    best: "Лучшее",
    worst: "Худшее",
    mean: "Среднее",
    avg5: "ao5",
    avg12: "ao12",
    mean3: "mo3",
    bestMo3: "Лучшее mo3",
    avg100: "ao100",
    avg1000: "ao1000",
    solves: "Сборки",
    attempts: "Попытки",
    moreStats: "Больше статистики",
  },
  statsDetails: {
    title: "Детали статистики",
    titleFor: "Детали статистики для",
    overview: "Обзор",
    averages: "Средние",
    records: "Рекорды",
    timeDistribution: "Распределение времён",
    progressChart: "График прогресса",
    sessionAnalysis: "Анализ сессии",
    predictions: "Прогнозы",
    standardDeviation: "Стандартное отклонение",
    bestSingle: "Лучший сингл",
    bestAo5: "Лучший ao5",
    bestAo12: "Лучший ao12",
    bestAo100: "Лучший ao100",
    bestAo1000: "Лучший ao1000",
    totalTime: "Общее время",
    averageTime: "Среднее время",
    solvesPerHour: "Сборок/час",
    consistency: "Стабильность",
    nextAo5: "Следующая цель ao5",
    nextAo12: "Следующая цель ao12",
    improvementRate: "Скорость улучшения",
    targetTime: "Целевое время",
    currentSession: "Текущая сессия",
    allSessions: "Все сессии",
    importTimes: "Импорт времён",
    exportJSON: "Экспорт JSON",
    exportCSV: "Экспорт CSV",
  },
  solveDetails: {
    title: "Детали сборки",
    time: "Время",
    date: "Дата",
    scramble: "Скрамбл",
    editedScramble: "Отредактированная скрамбл",
    copyScramble: "Копировать скрамбл",
    penalty: "Штраф",
    none: "Нет",
    comment: "Комментарий",
    addComment: "Добавить комментарий...",
    save: "Сохранить",
    share: "Поделиться",
    plusTwo: "+2",
    dnf: "DNF",
  },
  gestures: {
    scrambleCopied: "Скрамбл скопирована",
    noSolvesToDelete: "Нет сборок для удаления",
    solveDeleted: "Сборка удалена",
    cannotAddPenaltyMBLD: "Нельзя добавить штраф к MBLD сборке",
    dnfRemoved: "DNF убран",
    dnfAdded: "DNF добавлен",
    plus2Added: "Штраф +2 добавлен",
    penaltyRemoved: "Штраф убран",
    newScrambleGenerated: "Новая скрамбл сгенерирована",
    timesPanelOpened: "Панель времён открыта",
  },
  times: {
    title: "Времена сборок",
    clear: "Очистить времена",
    close: "Закрыть",
    delete: "Удалить время",
    confirmClear:
      "Вы уверены, что хотите очистить все времена для этой дисциплины?",
    confirmDelete: "Вы уверены, что хотите удалить это время?",
  },
  buttons: {
    viewTimes: "Посмотреть времена",
    ok: "ОК",
    cancel: "Отмена",
  },
  events: {
    333: "3×3×3",
    222: "2×2×2",
    444: "4×4×4",
    555: "5×5×5",
    666: "6×6×6",
    777: "7×7×7",
    "333bf": "3×3×3 Вслепую",
    "333fm": "3×3×3 Наименьшее количество ходов",
    "333oh": "3×3×3 Одной рукой",
    clock: "Клок",
    minx: "Мегаминкс",
    pyram: "Пираминкс",
    skewb: "Скьюб",
    sq1: "Скваер-1",
    "444bf": "4×4×4 Вслепую",
    "555bf": "5×5×5 Вслепую",
    "333mbf": "3×3×3 Мульти-вслепую",
  },
  mbld: {
    cubeCount: "Кубики",
    solvedCount: "Решённые кубики",
    totalCount: "Всего кубиков",
    totalCubes: "Всего кубиков",
    cubesSolved: "Решённые кубики",
    bestPoints: "Лучшие очки",
    successRate: "Процент успеха",
    points: "очков",
    save: "Сохранить результат",
    visualizations: "Визуализации мульти-вслепую",
    scrambles: "Скрамблы мульти-вслепую",
    enterValidNumber:
      "Пожалуйста, введите корректное количество решённых кубиков.",
    noScrambles:
      "Нет доступных MBLD скрамблов. Пожалуйста, сначала выберите дисциплину 3×3×3 Мульти-вслепую.",
    visualizationNotFound:
      "Модальное окно визуализации не найдено. Пожалуйста, обновите страницу и попробуйте снова.",
    containerNotFound:
      "Контейнер визуализации не найден. Пожалуйста, обновите страницу и попробуйте снова.",
    clickToView: "Нажмите, чтобы увидеть все визуализации и скрамблы кубиков",
    bestScore: "Лучший счёт",
    worstScore: "Худший счёт",
    meanScore: "Средний счёт",
    averageScore: "Средний счёт",
    attempts: "попыток",
    totalAttempts: "Всего попыток",
    clickToViewScrambles: "Нажмите, чтобы увидеть все скрамблы",
    clickToViewScramblesCount: "Нажмите, чтобы увидеть все {0} скрамблов",
    setup: "Настройка мульти-вслепую",
    results: "Результаты мульти-вслепую",
    generateScrambles: "Генерировать скрамблы",
    saveResult: "Сохранить результат",
    cubeNumber: "Кубик",
    numberOfCubesMinimum: "Количество кубиков (минимум 2):",
    numberOfCubesSolved: "Количество решённых кубиков:",
    saveFirst: "Пожалуйста, сначала сохраните ваш результат.",
    visualizationsTitle: "Визуализации мульти-вслепую ({0} кубиков)",
    timeLimit: "Лимит времени: {0} минут",
    timeLimitExceeded: "Лимит времени превышен. Результат будет DNF.",
    negativePoints: "Отрицательные очки. Результат будет DNF.",
  },
  modals: {
    error: "Ошибка",
    warning: "Предупреждение",
    info: "Информация",
    confirm: "Подтвердить",
    prompt: "Требуется ввод",
  },
  stackmat: {
    error: "Ошибка Stackmat",
    noMicrophone:
      "Не удалось запустить таймер Stackmat: Микрофон не найден. Пожалуйста, подключите микрофон и попробуйте снова.",
    connected: "Подключён",
    disconnected: "Отключён",
    settingUp: "Настройка...",
  },
  sessions: {
    newSessionTitle: "Новая сессия",
    editSessionTitle: "Редактировать сессию",
    sessionName: "Название сессии:",
    sessionNamePlaceholder: "Моя сессия",
    puzzleType: "Тип головоломки:",
    create: "Создать",
    save: "Сохранить",
  },
  scramble: {
    loading: "Загрузка скрамбла...",
  },
  debug: {
    timerState: "Состояние таймера: ",
    spaceHeldFor: "Пробел удерживается: ",
    currentEvent: "Текущая дисциплина: ",
    scrambleSource: "Источник скрамбла: ",
  },
  fmc: {
    title: "Вызов наименьшего количества ходов",
    info: "Решите кубик наименьшим количеством ходов. У вас есть 60 минут, чтобы найти решение.",
    timeRemaining: "Оставшееся время:",
    scramble: "Скрамбл:",
    solution: "Решение:",
    moveCount: "Ходы:",
    moves: "ходов",
    submit: "Отправить",
    resultTitle: "Результат FMC",
    resultTime: "Время:",
    resultSolution: "Решение:",
    resultOk: "ОК",
    solutionPlaceholder:
      "Введите ваше решение здесь, используя стандартную WCA нотацию...",
    notationHelp: "Помощь по нотации:",
    notationHelpContent:
      "Повороты граней: U, D, L, R, F, B (с суффиксами ' или 2)<br>Широкие повороты: Uw, Dw, и т.д.<br>Повороты слоёв: M, E, S<br>Вращения: x, y, z (не считаются в общем количестве ходов)",
    submitSolution: "Отправить решение",
    validSolution: "Корректное решение",
    invalidNotation: "Обнаружена некорректная нотация",
    bestMoves: "Лучшие ходы",
    worstMoves: "Худшие ходы",
    meanMoves: "Средние ходы",
    bestMo3: "Лучшее mo3",
    averageMoves: "Средние ходы",
    attempts: "попыток",
    totalAttempts: "Всего попыток",
    tooManyMoves: "Решение превышает лимит в 80 ходов",
    timeExceeded:
      "Лимит времени превышен. Ваше решение будет помечено как DNF, если не отправлено.",
    confirmClose:
      "Вы уверены, что хотите закрыть? Ваша попытка будет помечена как DNF.",
    dnfReasonTimeout: "Лимит времени превышен",
    dnfReasonInvalid: "Некорректная нотация",
    dnfReasonTooManyMoves: "Решение превышает 80 ходов",
    dnfReasonAbandoned: "Попытка прервана",
    confirmSubmit: "Вы уверены, что хотите отправить ваше решение?",
    pressToStart: "Нажмите пробел, чтобы начать попытку FMC",
    solutionAccepted: "Решение принято",
    clickToViewTwizzle:
      "Нажмите на ссылку ниже, чтобы посмотреть решение в Twizzle",
    viewOnTwizzle: "Посмотреть в Twizzle",
    moveCountLabel: "Количество ходов:",
    movesHTM: "ходов (HTM)",
    timeUsedLabel: "Использованное время:",
    loadingFMC: "загрузка FMC",
    generatingScramble: "генерация скрамбла и подготовка интерфейса",
  },
  tutorial: {
    welcomeTitle: "Добро пожаловать в scTimer!",
    welcomeSubtitle: "Ваш профессиональный таймер для спидкубинга",
    selectLanguage: "Выберите язык:",
    feature1: "Стандартный WCA таймер",
    feature2: "Продвинутая статистика",
    feature3: "Все WCA дисциплины",
    feature4: "Генератор скрамблов",
    welcomeDescription:
      "Хотели бы вы пройти быстрый тур, чтобы узнать, как эффективно использовать scTimer? Учебник проведёт вас через основные функции всего за несколько шагов.",
    skipTutorial: "Пропустить учебник",
    startTour: "Начать тур",
    step1: {
      title: "Отображение скрамбла",
      text: "Здесь показывается последовательность скрамбла для вашей текущей головоломки. Каждый скрамбл генерируется случайно в соответствии со стандартами WCA.",
    },
    step2: {
      title: "Управление таймером",
      text: "Нажмите и удерживайте ПРОБЕЛ, чтобы начать отсчёт времени, отпустите, чтобы начать решение. На мобильных устройствах нажмите и удерживайте область таймера. Таймер следует стандартам инспекции WCA.",
    },
    step3: {
      title: "Выбор дисциплины",
      text: "Выберите из всех WCA дисциплин, включая 3x3x3, 2x2x2, 4x4x4 и многие другие типы головоломок. Нажмите или коснитесь, чтобы открыть выпадающее меню.",
    },
    step4: {
      title: "Отслеживание статистики",
      text: "Отслеживайте свой прогресс с подробной статистикой, включая лучшее время, средние из 5, 12 и 100 сборок. Нажмите на любую статистику, чтобы увидеть больше деталей.",
    },
    step5: {
      title: "Генерация нового скрамбла",
      text: "Генерируйте новый скрамбл, когда будете готовы к следующей сборке. Горячая клавиша: Нажмите N или нажмите на иконку перемешивания.",
    },
    step6: {
      title: "Настройки и персонализация",
      text: "Настройте ваш опыт работы с таймером с помощью времени инспекции, звуковых опций, режимов таймера и настроек отображения. Горячая клавиша: Нажмите S.",
    },
    step7: {
      title: "Горячие клавиши",
      text: "Освойте эти горячие клавиши: ПРОБЕЛ (запуск/остановка таймера), N (новый скрамбл), S (настройки), ESC (закрыть модальные окна), Стрелки (навигация). На мобильных устройствах используйте жесты свайпа!",
    },
    step8: {
      title: "Мобильные жесты",
      text: "На мобильных устройствах: Свайп влево, чтобы открыть панель времён, свайп вправо, чтобы закрыть её, нажмите и удерживайте таймер для запуска, двойное нажатие на скрамбл для копирования. Сжимайте для масштабирования визуализаций.",
    },
    step9: {
      title: "Профессиональные советы и функции",
      text: "Включите время инспекции в настройках для практики WCA. Используйте разные сессии для отслеживания различных дисциплин. Экспортируйте ваши времена для анализа. Таймер работает офлайн как PWA!",
    },
    previous: "Назад",
    next: "Далее",
    finish: "Завершить",
    close: "Закрыть",
    stepCounter: "из",
    restartTutorial: "Перезапустить учебник",
  },
};
